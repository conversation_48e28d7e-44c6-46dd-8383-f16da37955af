import { prisma } from "../prisma/prisma";


async function main() {

const t = "sdfsd";

console.log("t", t);

const transfer = await prisma.transfer.findFirst({
  where: {
   id: "adf18095-00dc-4476-958f-eb207aa000a5",
  },
  include: {
    reconciliations: {
      where: {
        status: {
          in: ["AUTO_MATCHED", "MANUALLY_MATCHED"],
        },
      },
    },
  },
});

console.log("transfer", transfer);
}

main();